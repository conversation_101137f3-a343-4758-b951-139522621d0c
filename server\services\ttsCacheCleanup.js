/**
 * TTS缓存清理服务
 */
const schedule = require('node-schedule');
const TtsCache = require('../models/TtsCache');
const { getConnection } = require('../services/database');

// 用于跟踪服务是否已初始化
let isInitialized = false;
let currentJob = null;

/**
 * 清理非VIP用户的过期TTS缓存记录
 * 只针对非VIP用户或VIP已过期的用户进行清理
 */
const cleanExpiredCacheForNonVip = async () => {
  try {
    // console.log('[TtsCache] 开始清理非VIP用户的过期TTS缓存记录...');

    // 先更新已过期的VIP用户状态
    const pool = await getConnection();
    await pool.query(`
      UPDATE users
      SET vip_status = 'expired'
      WHERE vip_status = 'active' AND vip_end_date < NOW()
    `);

    // 获取过期时间统计
    const [countRows] = await pool.query(
      `SELECT COUNT(*) as total,
              MIN(expires_at) as earliest,
              MAX(expires_at) as latest,
              SUM(OCTET_LENGTH(audio_data))/1048576 as total_size_mb
       FROM tts_cache tc
       LEFT JOIN users u ON tc.user_id = u.id
       WHERE u.id IS NULL OR
             u.vip_status IS NULL OR
             u.vip_status = 'none' OR
             u.vip_status = 'expired'`
    );

    if (countRows.length > 0) {
      const { total, earliest, latest, total_size_mb } = countRows[0];
      const formattedSize = total_size_mb && !isNaN(total_size_mb) ?
        Number(total_size_mb).toFixed(2) : '0';
      console.log(`[TtsCache] 当前非VIP用户缓存统计: 总记录数=${total}, 最早过期=${earliest || 'N/A'}, 最晚过期=${latest || 'N/A'}, 总大小≈${formattedSize}MB`);
    }

    // 获取VIP用户缓存统计
    const [vipCountRows] = await pool.query(
      `SELECT COUNT(*) as total,
              MIN(expires_at) as earliest,
              MAX(expires_at) as latest,
              SUM(OCTET_LENGTH(audio_data))/1048576 as total_size_mb
       FROM tts_cache tc
       INNER JOIN users u ON tc.user_id = u.id
       WHERE u.vip_status != 'none' AND u.vip_end_date >= NOW()`
    );

    if (vipCountRows.length > 0) {
      const { total, earliest, latest, total_size_mb } = vipCountRows[0];
      const formattedSize = total_size_mb && !isNaN(total_size_mb) ?
        Number(total_size_mb).toFixed(2) : '0';
      console.log(`[TtsCache] 当前VIP用户缓存统计: 总记录数=${total}, 总大小≈${formattedSize}MB`);
    }

    // 计算非VIP用户过期记录数量
    const [expiredRows] = await pool.query(
      `SELECT COUNT(*) as expired,
              SUM(OCTET_LENGTH(tc.audio_data))/1048576 as expired_size_mb
       FROM tts_cache tc
       LEFT JOIN users u ON tc.user_id = u.id
       WHERE tc.expires_at < NOW() AND (
         u.id IS NULL OR
         u.vip_status IS NULL OR
         u.vip_status = 'none' OR
         u.vip_status = 'expired'
       )`
    );

    if (expiredRows.length > 0) {
      const expired = expiredRows[0].expired || 0;
      const expiredSizeMB = expiredRows[0].expired_size_mb;
      const formattedExpiredSize = expiredSizeMB && !isNaN(expiredSizeMB) ?
        Number(expiredSizeMB).toFixed(2) : '0';
      // console.log(`[TtsCache] 待清理的非VIP用户过期记录数: ${expired}, 约占${formattedExpiredSize}MB空间`);
    }

    // 1. 先清理未登录用户的24小时缓存
    const anonymousDeletedCount = await TtsCache.cleanAnonymousUserCache();

    // 2. 再清理非VIP用户的过期记录
    const startTime = Date.now();
    const deletedCount = await TtsCache.cleanExpiredForNonVipUsers();
    const elapsedTime = Date.now() - startTime;

    const totalDeleted = anonymousDeletedCount + deletedCount;
    console.log(`[TtsCache] 清理完成! 删除了 ${deletedCount} 条非VIP用户的过期TTS缓存记录, 耗时: ${elapsedTime}ms`);
    return totalDeleted;
  } catch (error) {
    console.error('[TtsCache] 清理非VIP用户的过期TTS缓存时出错:', error);
    return 0;
  }
};

/**
 * 初始化TTS缓存清理服务
 * @param {string} [cronExpression='0 * * * *'] - Cron表达式，默认每小时执行一次（整点）
 * @returns {Object|null} 调度任务对象或null
 */
const initTtsCacheCleanup = (cronExpression = '0 * * * *') => {
  try {
    // 单例模式：如果服务已经初始化，则取消之前的任务并重新调度
    if (isInitialized) {
      // console.log(`[TtsCache] 服务已经初始化，正在更新调度规则: ${cronExpression}`);
      if (currentJob) {
        currentJob.cancel();
        // console.log('[TtsCache] 已取消之前的调度任务');
      }
    } else {
      // console.log(`[TtsCache] 首次初始化TTS缓存清理服务`);
      isInitialized = true;
    }
    
    // 设置定时清理任务 - 清理非VIP用户的音频
    const job = schedule.scheduleJob(cronExpression, cleanExpiredCacheForNonVip);
    
    // 保存当前任务引用
    currentJob = job;
    
    // console.log(`[TtsCache] TTS缓存清理服务已${isInitialized ? '更新' : '启动'}，调度表达式: ${cronExpression}`);
    // console.log(`[TtsCache] 下次执行时间: ${job.nextInvocation().toISOString()}`);
    
    // 服务启动时也执行一次清理 - 仅清理非VIP用户音频
    // console.log(`[TtsCache] 系统启动时执行初始化清理非VIP用户音频...`);
    cleanExpiredCacheForNonVip().then(count => {
      // console.log(`[TtsCache] 初始化清理完成，删除了${count}条非VIP用户的过期记录`);
    });
    
    return job;
  } catch (error) {
    console.error('[TtsCache] 初始化缓存清理服务失败:', error);
    return null;
  }
};

/**
 * 关闭TTS缓存清理服务
 */
const shutdownTtsCacheCleanup = () => {
  if (currentJob) {
    currentJob.cancel();
    console.log('[TtsCache] 缓存清理服务已关闭');
    currentJob = null;
  }
  isInitialized = false;
};

module.exports = {
  initTtsCacheCleanup,
  cleanExpiredCacheForNonVip,
  shutdownTtsCacheCleanup
}; 